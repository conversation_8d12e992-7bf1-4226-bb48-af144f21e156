FROM apache/tika:latest

# Set environment variables for performance and custom config
ENV JAVA_OPTS="-Xmx2g -Xms512m -Djava.awt.headless=true -Djava.security.egd=file:/dev/./urandom"
ENV TIKA_CONFIG_PATH="/tmp/tika-config.xml"

# Switch to root to set up custom files and permissions
USER root

# Create temp and log directories with open permissions
RUN mkdir -p /tmp/tika-temp && \
    chmod 777 /tmp/tika-temp && \
    mkdir -p /tmp/tika-logs && \
    chmod 777 /tmp/tika-logs

# Copy your custom Tika config into the container
COPY tika-config.xml /tmp/tika-config.xml

# Set permissions for the config file and folders
RUN chmod 644 /tmp/tika-config.xml && \
    chmod 777 /tmp/tika-temp /tmp/tika-logs

# Expose Tika's default port
EXPOSE 9998

# Health check to monitor service availability
HEALTHCHECK --interval=30s --timeout=10s --retries=3 --start-period=40s \
  CMD curl -f http://localhost:9998/ || exit 1

# ✅ Do NOT override CMD — the base image already knows how to start Tika using the provided config
